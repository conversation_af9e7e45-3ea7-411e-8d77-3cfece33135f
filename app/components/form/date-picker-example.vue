<script setup lang="ts">
/**
 * Example usage of the DatePicker component
 * This file demonstrates how to use the date picker in forms with validation
 */

import * as v from 'valibot';
import type { FormSubmitEvent } from '#ui/types';
import DatePicker from './date-picker.vue';

// Form validation schema using Valibot
const exampleSchema = v.object({
  start_date: v.pipe(v.string(), v.minLength(1, 'Start date is required')),
  end_date: v.pipe(v.string(), v.minLength(1, 'End date is required')),
  optional_date: v.optional(v.string()),
});

type ExampleSchema = v.InferOutput<typeof exampleSchema>;

// Form state
const formState = reactive({
  start_date: '',
  end_date: '',
  optional_date: '',
});

// Set default values
onMounted(() => {
  // Set start date to today
  formState.start_date = new Date().toISOString().split('T')[0];
  // Set end date to 30 days from now
  formState.end_date = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
});

// Form submission
const isSubmitting = ref(false);
const formError = ref('');

async function handleSubmit(event: FormSubmitEvent<ExampleSchema>) {
  isSubmitting.value = true;
  formError.value = '';
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('Form submitted:', event.data);
    
    // Show success message
    const toast = useToast();
    toast.add({
      title: 'Success',
      description: 'Dates saved successfully!',
      color: 'success',
    });
  } catch (error) {
    formError.value = 'Failed to save dates. Please try again.';
  } finally {
    isSubmitting.value = false;
  }
}

// Computed properties for validation
const minEndDate = computed(() => formState.start_date || undefined);
const maxStartDate = computed(() => formState.end_date || undefined);
</script>

<template>
  <div class="max-w-2xl mx-auto p-6">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">
        Date Picker Component Example
      </h1>
      <p class="text-gray-600">
        This example demonstrates how to use the DatePicker component with form validation.
      </p>
    </div>

    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">
          Date Range Form
        </h2>
      </template>

      <UForm
        :schema="exampleSchema"
        :state="formState"
        class="space-y-6"
        @submit="handleSubmit"
      >
        <!-- Required start date -->
        <DatePicker
          v-model="formState.start_date"
          label="Start Date"
          name="start_date"
          :max="maxStartDate"
          required
          help="Select the start date for your project"
        />

        <!-- Required end date -->
        <DatePicker
          v-model="formState.end_date"
          label="End Date"
          name="end_date"
          :min="minEndDate"
          required
          help="Select the end date for your project"
        />

        <!-- Optional date -->
        <DatePicker
          v-model="formState.optional_date"
          label="Optional Date"
          name="optional_date"
          placeholder="Select an optional date"
          help="This date is optional and can be left empty"
        />

        <!-- Error display -->
        <UAlert
          v-if="formError"
          color="error"
          variant="subtle"
          :title="formError"
        />

        <!-- Submit button -->
        <div class="flex justify-end">
          <UButton
            type="submit"
            :loading="isSubmitting"
            size="lg"
          >
            Save Dates
          </UButton>
        </div>
      </UForm>
    </UCard>

    <!-- Current values display -->
    <UCard class="mt-8">
      <template #header>
        <h3 class="text-lg font-semibold">
          Current Values
        </h3>
      </template>

      <div class="space-y-2">
        <div>
          <span class="font-medium">Start Date:</span>
          <span class="ml-2 text-gray-600">{{ formState.start_date || 'Not set' }}</span>
        </div>
        <div>
          <span class="font-medium">End Date:</span>
          <span class="ml-2 text-gray-600">{{ formState.end_date || 'Not set' }}</span>
        </div>
        <div>
          <span class="font-medium">Optional Date:</span>
          <span class="ml-2 text-gray-600">{{ formState.optional_date || 'Not set' }}</span>
        </div>
      </div>
    </UCard>
  </div>
</template>
