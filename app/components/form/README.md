# Form Components

This directory contains reusable form components for the Air Invoice application.

## DatePicker Component

A reusable date picker component that wraps NuxtUI's UInput with date type and provides consistent styling, validation, and v-model support.

### Features

- ✅ **v-model support** for two-way data binding
- ✅ **TypeScript interfaces** for type safety
- ✅ **Validation integration** with Valibot schemas
- ✅ **Consistent styling** with existing form components
- ✅ **Accessibility** features built-in
- ✅ **Min/Max date constraints**
- ✅ **Error state handling**
- ✅ **Multiple sizes** (sm, md, lg)
- ✅ **Help text support**
- ✅ **Required field indication**

### Basic Usage

```vue
<script setup lang="ts">
const selectedDate = ref('');
</script>

<template>
  <DatePicker
    v-model="selectedDate"
    label="Select Date"
    name="date"
    required
  />
</template>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `string \| null` | `null` | The selected date value (ISO string format YYYY-MM-DD) |
| `label` | `string` | `''` | Label text for the form field |
| `placeholder` | `string` | `'Select a date'` | Placeholder text for the input |
| `required` | `boolean` | `false` | Whether the field is required |
| `disabled` | `boolean` | `false` | Whether the field is disabled |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Size of the input field |
| `name` | `string` | `''` | Name attribute for form validation |
| `min` | `string` | `undefined` | Minimum date (ISO string format YYYY-MM-DD) |
| `max` | `string` | `undefined` | Maximum date (ISO string format YYYY-MM-DD) |
| `help` | `string` | `''` | Help text to display below the input |
| `error` | `string` | `''` | Error message to display |

### Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update:modelValue` | `string \| null` | Emitted when the date value changes |
| `blur` | `Event` | Emitted when the input loses focus |
| `focus` | `Event` | Emitted when the input gains focus |

### Usage with Form Validation

```vue
<script setup lang="ts">
import * as v from 'valibot';
import type { FormSubmitEvent } from '#ui/types';

// Validation schema
const schema = v.object({
  start_date: v.pipe(v.string(), v.minLength(1, 'Start date is required')),
  end_date: v.pipe(v.string(), v.minLength(1, 'End date is required')),
});

type Schema = v.InferOutput<typeof schema>;

// Form state
const formState = reactive({
  start_date: '',
  end_date: '',
});

async function handleSubmit(event: FormSubmitEvent<Schema>) {
  console.log('Form data:', event.data);
}
</script>

<template>
  <UForm
    :schema="schema"
    :state="formState"
    @submit="handleSubmit"
  >
    <DatePicker
      v-model="formState.start_date"
      label="Start Date"
      name="start_date"
      required
    />
    
    <DatePicker
      v-model="formState.end_date"
      label="End Date"
      name="end_date"
      :min="formState.start_date"
      required
    />
    
    <UButton type="submit">
      Submit
    </UButton>
  </UForm>
</template>
```

### Date Range Example

```vue
<script setup lang="ts">
const startDate = ref('');
const endDate = ref('');

// Computed properties for validation
const minEndDate = computed(() => startDate.value || undefined);
const maxStartDate = computed(() => endDate.value || undefined);
</script>

<template>
  <div class="space-y-4">
    <DatePicker
      v-model="startDate"
      label="Start Date"
      name="start_date"
      :max="maxStartDate"
      required
    />
    
    <DatePicker
      v-model="endDate"
      label="End Date"
      name="end_date"
      :min="minEndDate"
      required
    />
  </div>
</template>
```

### Integration with Existing Invoice Forms

The DatePicker component can easily replace existing date inputs in invoice forms:

```vue
<!-- Before -->
<UFormField label="Issue Date" name="issue_date" required>
  <UInput
    v-model="invoiceFormState.issue_date"
    type="date"
    class="w-full"
  />
</UFormField>

<!-- After -->
<DatePicker
  v-model="invoiceFormState.issue_date"
  label="Issue Date"
  name="issue_date"
  required
/>
```

### Styling

The component inherits all styling from NuxtUI's UFormField and UInput components, ensuring consistency with the rest of the application. Custom styling can be applied through the `class` prop or by modifying the component's scoped styles.

### Accessibility

The component includes built-in accessibility features:
- Proper ARIA labels and descriptions
- Keyboard navigation support
- Screen reader compatibility
- Focus management

### Browser Support

The component uses the native HTML5 date input type, which is supported in all modern browsers. For older browsers, it gracefully falls back to a text input.
